$cursorPath = "C:\Users\<USER>\AppData\Local\Programs\Cursor\Cursor.exe"
$vsixPath = "$env:TEMP\AugmentCode\augment.vsix"

Write-Host "Đang cài đặt AugmentCode extension vào Cursor..."
Write-Host "Đường dẫn Cursor: $cursorPath"
Write-Host "Đường dẫn VSIX: $vsixPath"

# Kiểm tra xem file VSIX có tồn tại không
if (Test-Path $vsixPath) {
    Write-Host "File VSIX đã được tìm thấy. Tiến hành cài đặt..."
    
    # Cài đặt extension
    try {
        $process = Start-Process -FilePath $cursorPath -ArgumentList "--install-extension", "`"$vsixPath`"" -Wait -PassThru
        
        if ($process.ExitCode -eq 0) {
            Write-Host "Cài đặt AugmentCode thành công!"
        } else {
            Write-Host "Cài đặt thất bại với mã lỗi: $($process.ExitCode)"
        }
    }
    catch {
        Write-Host "Lỗi khi cài đặt extension: $_"
    }
} else {
    Write-Host "Không tìm thấy file VSIX tại đường dẫn: $vsixPath"
    Write-Host "Đang tải xuống file VSIX..."
    
    # Tạo thư mục nếu chưa tồn tại
    if (-not (Test-Path "$env:TEMP\AugmentCode")) {
        New-Item -Path "$env:TEMP\AugmentCode" -ItemType Directory -Force | Out-Null
    }
    
    # Tải xuống file VSIX
    try {
        Invoke-WebRequest -Uri "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/augment/vsextensions/vscode-augment/latest/vspackage" -OutFile $vsixPath
        Write-Host "Đã tải xuống file VSIX thành công. Tiến hành cài đặt..."
        
        # Cài đặt extension
        $process = Start-Process -FilePath $cursorPath -ArgumentList "--install-extension", "`"$vsixPath`"" -Wait -PassThru
        
        if ($process.ExitCode -eq 0) {
            Write-Host "Cài đặt AugmentCode thành công!"
        } else {
            Write-Host "Cài đặt thất bại với mã lỗi: $($process.ExitCode)"
        }
    }
    catch {
        Write-Host "Lỗi khi tải xuống hoặc cài đặt extension: $_"
    }
}

Write-Host "Nhấn Enter để thoát..."
Read-Host 