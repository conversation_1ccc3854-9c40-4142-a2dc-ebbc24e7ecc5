#!/usr/bin/env python3
"""
AugmentCode-Free chương trình chính
Khởi động giao diện người dùng đồ họa

Đây là chương trình khởi động chính của công cụ AugmentCode-Free.
Nhấp đúp vào file này hoặc chạy 'python main.py' trong dòng lệnh để khởi động giao diện GUI.

C<PERSON>c tính năng bao gồm:
- Xóa cơ sở dữ liệu VS Code
- Sửa ID Telemetry của VS Code  
- Chạy tất cả các công cụ có sẵn
"""

import sys
import os
from pathlib import Path

def main():
    """Hàm chính - Khởi động ứng dụng GUI"""
    print("=" * 50)
    print("🚀 Đang khởi động công cụ AugmentCode-Free...")
    print("=" * 50)
    print()
    
    # Thêm thư mục hiện tại vào đường dẫn Python
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    try:
        # Nhập và khởi động GUI
        from gui import main as gui_main
        
        print("✅ Đang khởi động giao diện đồ họa...")
        print("💡 Gợi ý: Nếu giao diện không xuất hiện, hãy kiểm tra xem có tường lửa hoặc phần mềm bảo mật nào đang chặn không")
        print()
        
        # Khởi động GUI
        gui_main()
        
    except ImportError as e:
        print(f"❌ Lỗi nhập: {e}")
        print()
        print("🔧 Giải pháp:")
        print("1. Đảm bảo tất cả các thư viện phụ thuộc đã được cài đặt: pip install -r requirements.txt")
        print("2. Đảm bảo phiên bản Python là 3.7 hoặc cao hơn")
        print("3. Đảm bảo tất cả các file dự án đều nằm trong cùng một thư mục")
        print("4. Đối với các vấn đề khác, vui lòng gửi issue")
        input("\nNhấn Enter để thoát...")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Lỗi khi khởi động GUI: {e}")
        print()
        print("🔧 Giải pháp có thể:")
        print("1. Cài đặt lại các thư viện phụ thuộc: pip install -r requirements.txt")
        print("2. Kiểm tra xem môi trường Python có được cấu hình đúng không")
        print("3. Đảm bảo có đủ quyền hệ thống")
        print("4. Đối với các vấn đề khác, vui lòng gửi issue")
        input("\nNhấn Enter để thoát...")
        sys.exit(1)


if __name__ == "__main__":
    main()
